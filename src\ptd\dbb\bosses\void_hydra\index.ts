import { Entity } from "@minecraft/server";
import { selectAttack } from "./controller";
import { getTarget } from "../general_mechanics/targetUtils";

// Import all attack functions
import { executeRightAtomicCrossAttack } from "./attacks/rightAtomicCross";
import { executeRightAtomicAttack } from "./attacks/rightAtomic";
import { executeRightVacuumAttack } from "./attacks/rightVacuum";
import { executeRightSummonAttack } from "./attacks/rightSummon";
import { executeMidAtomicAttack } from "./attacks/midAtomic";
import { executeMidMeteorAttack } from "./attacks/midMeteor";
import { executeMidSingularityAttack } from "./attacks/midSingularity";
import { executeLeftAtomicCrossAttack } from "./attacks/leftAtomicCross";
import { executeLeftAtomicAttack } from "./attacks/leftAtomic";
import { executeLeftRailgunAttack } from "./attacks/leftRailgun";
import { executeLeftMissileAttack } from "./attacks/leftMissile";
import { executeLeftShoutAttack } from "./attacks/leftShout";

/**
 * Handles the void hydra boss mechanics using event-driven system
 * Called when the environment sensor triggers an attack
 *
 * @param voidHydra The void hydra entity
 */
export function voidHydraMechanics(voidHydra: Entity): void {
  try {
    // Skip if entity is not valid
    if (!voidHydra) return;

    // Skip if entity is spawning or dead
    const isSpawning = voidHydra.getProperty("ptd_dbb:spawning") as boolean;
    const isDead = voidHydra.getProperty("ptd_dbb:dead") as boolean;
    if (isSpawning || isDead) return;

    // Handle attack mechanics using event-driven system
    const attack = voidHydra.getProperty("ptd_dbb:attack") as string;
    const coolingDown = voidHydra.getProperty("ptd_dbb:cooling_down") as boolean;

    if (attack && attack !== "none") {
      // Attack is active - execution is handled by individual attack functions using runTimeout
      // Execute attack-specific logic once when attack starts
      executeAttackLogic(voidHydra, attack);
      return;
    } else if (!coolingDown) {
      // No attack is active and not cooling down, handle attack selection
      const target = getTarget(voidHydra, voidHydra.location, 32, ["void_hydra"]);
      if (target) {
        selectAttack(voidHydra, target);

        // After selecting attack, immediately execute it if one was selected
        const selectedAttackType = voidHydra.getProperty("ptd_dbb:attack") as string;
        if (selectedAttackType && selectedAttackType !== "none") {
          executeAttackLogic(voidHydra, selectedAttackType);
        }
      }
    }
  } catch (error) {
    console.warn(`Error in void hydra mechanics: ${error}`);
  }
}

/**
 * Executes attack-specific logic based on the current attack and timer
 * @param voidHydra The void hydra entity
 * @param attack The current attack type
 * @param attackTimer The current attack timer
 */
function executeAttackLogic(voidHydra: Entity, attack: string): void {
  try {
    switch (attack) {
      case "right_atomic_cross":
        executeRightAtomicCrossAttack(voidHydra);
        break;
      case "right_atomic":
        executeRightAtomicAttack(voidHydra);
        break;
      case "right_vacuum":
        executeRightVacuumAttack(voidHydra);
        break;
      case "right_summon":
        executeRightSummonAttack(voidHydra);
        break;
      case "mid_atomic":
        executeMidAtomicAttack(voidHydra);
        break;
      case "mid_meteor":
        executeMidMeteorAttack(voidHydra);
        break;
      case "mid_singularity":
        executeMidSingularityAttack(voidHydra);
        break;
      case "left_atomic_cross":
        executeLeftAtomicCrossAttack(voidHydra);
        break;
      case "left_atomic":
        executeLeftAtomicAttack(voidHydra);
        break;
      case "left_railgun":
        executeLeftRailgunAttack(voidHydra);
        break;
      case "left_missile":
        executeLeftMissileAttack(voidHydra);
        break;
      case "left_shout":
        executeLeftShoutAttack(voidHydra);
        break;
      default:
        // Unknown attack type
        break;
    }
  } catch (error) {
    console.warn(`Error executing attack ${attack}: ${error}`);
  }
}
